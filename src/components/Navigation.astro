---
import { getLocalizedUrl, getLanguageUrls } from '../i18n/utils.js';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'lucide-astro';

interface Props {
  currentPage?: string;
  lang?: string;
  translations?: any;
}

const { currentPage = 'home', lang = 'en', translations } = Astro.props;

// Get translation function
function t(key) {
  if (!translations) return key;
  const keys = key.split('.');
  let value = translations;
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      return key;
    }
  }
  return value;
}

// Get language URLs for current path
// Remove base path and language prefix to get the clean path
let currentPath = Astro.url.pathname;

// Remove base path if present
if (import.meta.env.BASE_URL && currentPath.startsWith(import.meta.env.BASE_URL)) {
  currentPath = currentPath.slice(import.meta.env.BASE_URL.length - 1); // Keep leading slash
}

// Remove language prefix (e.g., /sq/, /it/, etc.) but keep the rest
currentPath = currentPath.replace(/^\/[a-z]{2}\//, '/');

// Ensure we have a clean path
if (!currentPath || currentPath === '/') {
  currentPath = '/';
} else if (!currentPath.startsWith('/')) {
  currentPath = '/' + currentPath;
}
const languageUrls = getLanguageUrls(currentPath, lang);

// Language configuration with flag emojis
const languageConfig = {
  en: { label: 'English', flag: '🇬🇧' },
  sq: { label: 'Shqip', flag: '🇦🇱' },
  it: { label: 'Italiano', flag: '🇮🇹' },
  pl: { label: 'Polski', flag: '🇵🇱' },
  de: { label: 'Deutsch', flag: '🇩🇪' },
  fr: { label: 'Français', flag: '🇫🇷' }
};
---

<nav class="top-0 right-0 left-0 z-50 fixed transition-all duration-300 nav-bar">
  <div class="mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
    <div class="flex justify-between items-center h-16">
      <!-- Logo -->
      <a href={getLocalizedUrl('/', lang)} class="group flex items-center space-x-3">
        <img
          src={`${import.meta.env.BASE_URL}bar-joni-logo.jpg`}
          alt="Bar Joni Logo"
          width="40"
          height="40"
          class="rounded-full w-10 h-10 object-cover group-hover:scale-105 transition-transform"
        />
        <span class="font-bold text-white text-xl transition-colors brand-text">
          {t('navigation.brand')}
        </span>
      </a>

      <!-- Right side menus -->
      <div class="flex items-center space-x-4">
        <!-- Language Selector -->
        <div class="group relative language-menu-container">
          <button class="flex items-center space-x-2 bg-white/10 hover:bg-white/20 px-3 py-2 rounded-lg transition-colors language-menu-toggle">
            <span class="text-xl">{languageConfig[lang]?.flag}</span>
            <span class="hidden sm:block font-medium text-white text-sm language-text">{languageConfig[lang]?.label}</span>
            <svg class="w-4 h-4 text-white group-hover:rotate-180 transition-transform language-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>

          <!-- Language Dropdown -->
          <div class="invisible group-hover:visible top-full right-0 absolute opacity-0 group-hover:opacity-100 mt-2 w-48 transition-all translate-y-2 group-hover:translate-y-0 duration-300 transform language-menu">
            <div class="space-y-2 bg-white/95 shadow-lg backdrop-blur-md px-2 pt-2 pb-3 rounded-lg">
              {Object.entries(languageConfig).map(([langCode, langData]) => {
                const langUrl = languageUrls[langCode]?.url || getLocalizedUrl('/', langCode);
                const isActive = langCode === lang;
                return (
                  <a
                    href={langUrl}
                    class={`flex items-center space-x-3 px-4 py-4 min-h-[44px] rounded-md text-sm transition-colors language-nav-link ${isActive ? 'bg-blue-50 text-blue-600 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'}`}
                  >
                    <span class="text-lg">{langData.flag}</span>
                    <span>{langData.label}</span>
                    {isActive && <span class="ml-auto text-blue-600">✓</span>}
                  </a>
                );
              })}
            </div>
          </div>
        </div>

        <!-- Navigation Icons -->
        <div class="flex items-center space-x-3">
          <!-- Home Icon -->
          <a
            href={getLocalizedUrl('/', lang)}
            aria-label={t('navigation.home')}
            class="group flex justify-center items-center bg-white/10 hover:bg-white/20 p-3 rounded-lg transition-colors nav-icon-link"
          >
            <svg class="w-5 h-5 text-white group-hover:scale-110 transition-transform nav-icon" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
            </svg>
          </a>

          <!-- Beach Rules Icon -->
          <a
            href={getLocalizedUrl('/beach-rules', lang)}
            aria-label={t('navigation.beachRules')}
            class="group flex justify-center items-center bg-white/10 hover:bg-white/20 p-3 rounded-lg transition-colors nav-icon-link"
          >
            <Umbrella class="w-5 h-5 text-white group-hover:scale-110 transition-transform nav-icon" />
          </a>

          <!-- Menu Icon -->
          <a
            href={getLocalizedUrl('/menu', lang)}
            aria-label={t('navigation.menu')}
            class="group flex justify-center items-center bg-white/10 hover:bg-white/20 p-3 rounded-lg transition-colors nav-icon-link"
          >
            <Martini class="w-5 h-5 text-white group-hover:scale-110 transition-transform nav-icon" />
          </a>
        </div>
      </div>
    </div>
  </div>
</nav>

<style>
  .nav-bar {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(4px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .nav-bar.nav-scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(4px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .nav-bar.nav-hidden {
    transform: translateY(-100%);
  }

  .nav-link {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }

  .nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: currentColor;
    transition: width 0.3s ease;
  }

  .nav-link:hover::after,
  .nav-link.active::after {
    width: 100%;
  }

  .nav-scrolled .nav-link {
    color: rgb(55 65 81); /* text-gray-700 */
    text-shadow: none;
  }

  .nav-scrolled .nav-link:hover {
    color: rgb(37 99 235); /* text-blue-600 */
  }

  .nav-scrolled .nav-link.active {
    color: rgb(37 99 235); /* text-blue-600 */
    font-weight: 600; /* font-semibold */
  }

  /* Brand text color changes when scrolled */
  .nav-scrolled .brand-text {
    color: rgb(55 65 81); /* text-gray-700 */
  }

  /* Navigation icon improvements */
  .nav-icon {
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
  }

  /* Navigation icon color changes when scrolled */
  .nav-scrolled .nav-icon {
    color: rgb(55 65 81); /* text-gray-700 */
  }

  .nav-scrolled .nav-icon-link {
    background: rgba(243 244 246 / 1); /* bg-gray-100 */
  }

  .nav-scrolled .nav-icon-link:hover {
    background: rgba(229 231 235 / 1); /* bg-gray-200 */
  }

  /* Language selector color changes when scrolled */
  .nav-scrolled .language-text {
    color: rgb(55 65 81); /* text-gray-700 */
  }

  .nav-scrolled .language-icon {
    color: rgb(55 65 81); /* text-gray-700 */
  }

  .nav-scrolled .language-menu-toggle {
    background: rgba(243 244 246 / 1); /* bg-gray-100 */
  }

  .nav-scrolled .language-menu-toggle:hover {
    background: rgba(229 231 235 / 1); /* bg-gray-200 */
  }

  /* Language menu styles */
  .language-menu {
    z-index: 1000;
  }

  .language-menu-toggle {
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
  }

  /* Ensure language menu works on touch devices */
  @media (hover: none) and (pointer: coarse) {
    .language-menu-container:hover .language-menu {
      visibility: hidden;
      opacity: 0;
      transform: translateY(8px);
    }

    .language-menu.visible {
      visibility: visible !important;
      opacity: 1 !important;
      transform: translateY(0) !important;
    }
  }

  /* Language nav link improvements */
  .language-nav-link {
    min-height: 44px !important;
    height: auto !important;
    display: flex !important;
    align-items: center !important;
    padding: 12px 16px !important;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const nav = document.querySelector('.nav-bar');
    const languageToggle = document.querySelector('.language-menu-toggle');
    const languageMenu = document.querySelector('.language-menu');
    let lastScrollY = window.scrollY;
    let ticking = false;

    // Navigation scroll effects
    function updateNavigation() {
      const currentScrollY = window.scrollY;
      
      // Add scrolled class when scrolled down
      if (currentScrollY > 50) {
        nav?.classList.add('nav-scrolled');
      } else {
        nav?.classList.remove('nav-scrolled');
      }

      // Hide/show navigation based on scroll direction
      if (currentScrollY > 100) {
        if (currentScrollY > lastScrollY && currentScrollY > 200) {
          nav?.classList.add('nav-hidden');
        } else {
          nav?.classList.remove('nav-hidden');
        }
      } else {
        nav?.classList.remove('nav-hidden');
      }

      lastScrollY = currentScrollY;
      ticking = false;
    }

    function onScroll() {
      if (!ticking) {
        requestAnimationFrame(updateNavigation);
        ticking = true;
      }
    }

    window.addEventListener('scroll', onScroll, { passive: true });

    // Language navigation toggle functionality
    let isLanguageMenuOpen = false;

    function toggleLanguageMenu() {
      isLanguageMenuOpen = !isLanguageMenuOpen;
      if (languageMenu) {
        if (isLanguageMenuOpen) {
          languageMenu.classList.remove('invisible', 'opacity-0', 'translate-y-2');
          languageMenu.classList.add('visible', 'opacity-100', 'translate-y-0');
        } else {
          languageMenu.classList.add('invisible', 'opacity-0', 'translate-y-2');
          languageMenu.classList.remove('visible', 'opacity-100', 'translate-y-0');
        }
      }
    }

    // Handle language menu toggle click
    if (languageToggle) {
      languageToggle.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleLanguageMenu();
      });
    }

    // Close language menu when clicking outside
    document.addEventListener('click', function(e) {
      if (isLanguageMenuOpen && languageMenu && !languageMenu.contains(e.target) && !languageToggle?.contains(e.target)) {
        toggleLanguageMenu();
      }
    });

    const languageNavLinks = document.querySelectorAll('.language-nav-link');
    languageNavLinks.forEach(link => {
      link.addEventListener('click', function(e) {
        // Store the selected language preference
        const href = this.getAttribute('href');
        if (href) {
          // Extract language from URL (e.g., '/sq/' -> 'sq', '/en/' -> 'en')
          const langMatch = href.match(/\/([a-z]{2})\//);
          if (langMatch) {
            localStorage.setItem('preferred-language', langMatch[1]);
          } else if (href === '/' || href.endsWith('/')) {
            // Root URL means English
            localStorage.setItem('preferred-language', 'en');
          }
        }

        if (isLanguageMenuOpen) {
          toggleLanguageMenu();
        }
      });
    });

    // Smooth scrolling for navigation links
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
      link.addEventListener('click', function(e) {
        e.preventDefault();
        const targetId = this.getAttribute('href');
        const targetElement = document.querySelector(targetId);
        if (targetElement) {
          const navHeight = nav?.offsetHeight || 0;
          const targetPosition = targetElement.offsetTop - navHeight;

          window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
          });
        }
      });
    });
  });
</script>
